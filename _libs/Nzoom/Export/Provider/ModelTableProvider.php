<?php

namespace Nzoom\Export\Provider;

use Nzoom\Export\Entity\ExportTable;
use Nzoom\Export\Entity\ExportTableCollection;
use Nzoom\Export\Entity\ExportHeader;
use Nzoom\Export\Entity\ExportColumn;
use Nzoom\Export\Entity\ExportRecord;
use Nzoom\Export\Entity\ExportValue;

/**
 * Class ModelTableProvider
 *
 * Automatically discovers and extracts table data from Model objects
 * Looks for variables with 'type' => 'grouping' to identify tables
 */
class ModelTableProvider implements ExportTableProviderInterface
{
    /**
     * @var \Registry
     */
    private $registry;

    /**
     * @var array Reference column configuration
     */
    private $referenceColumn;

    /**
     * @var array Cache of discovered table headers by table type
     */
    private $tableHeaders = [];

    /**
     * @var array Configuration options
     */
    private $options;

    /**
     * ModelTableProvider constructor
     *
     * @param \Registry $registry
     * @param string $referenceColumnName The name of the reference column (e.g., 'full_num', 'customer_id')
     * @param string $referenceColumnLabel The label for the reference column (e.g., 'Document Number', 'Customer ID')
     * @param array $options Optional configuration (max_records_per_table, include_empty_tables, etc.)
     */
    public function __construct(\Registry $registry, string $referenceColumnName, string $referenceColumnLabel, array $options = [])
    {
        $this->registry = $registry;
        $this->referenceColumn = [
            'name' => $referenceColumnName,
            'label' => $referenceColumnLabel
        ];
        $this->options = array_merge($this->getDefaultOptions(), $options);
    }

    /**
     * Get reference column configuration
     *
     * @return array
     */
    public function getReferenceColumn(): array
    {
        return $this->referenceColumn;
    }

    /**
     * Get default options
     *
     * @return array
     */
    private function getDefaultOptions(): array
    {
        return [
            'include_empty_tables' => false,
            'date_format' => 'd.m.Y',
            'datetime_format' => 'd.m.Y H:i',
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function getTablesForRecord($record, array $options = []): ExportTableCollection
    {
        $collection = new ExportTableCollection();
        $mergedOptions = array_merge($this->options, $options);

        if (!($record instanceof \Model)) {
            return $collection;
        }

        try {
            // Discover all grouping variables in the model
            $groupingVars = $this->discoverGroupingVariables($record);

            foreach ($groupingVars as $varName => $groupingData) {
                // Handle different table types
                if (isset($groupingData['type']) && $groupingData['type'] === 'gt2') {
                    $table = $this->createTableFromGT2Data($record, $varName, $groupingData, $mergedOptions);
                } else {
                    $table = $this->createTableFromGroupingData($record, $varName, $groupingData, $mergedOptions);
                }

                if ($table && ($table->hasRecords() || $mergedOptions['include_empty_tables'])) {
                    $collection->addTable($table);
                }
            }
        } catch (\Exception $e) {
            // Log error but continue
            if (isset($this->registry['logger'])) {
                $this->registry['logger']->warn(
                    "Failed to extract tables from model: " . $e->getMessage()
                );
            }
        }

        return $collection;
    }

    /**
     * Discover all grouping variables in a model
     *
     * @param \Model $model
     * @return array Array of grouping variables with their data
     */
    private function discoverGroupingVariables(\Model $model): array
    {
        $groupingVars = [];

        try {

            // Get all variables from the model using the proper method
            // This will get the variables in the format needed for templates/exports
            if (!$model->checkForVariables()) {
                return $groupingVars;
            }

            // Ensure model is unsanitized to access variables
            $wasSanitized = $model->isSanitized();
            if ($wasSanitized) {
                $model->unsanitize();
            }

            $modelVars = $model->getVarsForTemplateAssoc();

            // Restore sanitization state
            if ($wasSanitized) {
                $model->sanitize();
            }

            // Check each variable to see if it has 'type' => 'grouping' or 'gt2'
            foreach ($modelVars??[] as $varName => $varData) {
                if (is_array($varData) && isset($varData['type']) && in_array($varData['type'], ['grouping','gt2'])) {
                    $groupingVars[$varName] = $varData;
                }
            }
        } catch (\Exception $e) {
            // Log error but continue
            if (isset($this->registry['logger'])) {
                $this->registry['logger']->warn(
                    "Failed to discover grouping variables from model: " . $e->getMessage()
                );
            }
        }

        return $groupingVars;
    }

    /**
     * Create a table from grouping data
     *
     * @param \Model $model
     * @param string $varName
     * @param array $groupingData
     * @param array $options
     * @return ExportTable|null
     */
    private function createTableFromGroupingData(\Model $model, string $varName, array $groupingData, array $options): ?ExportTable
    {
        // Extract table structure from grouping data
        $names = $groupingData['names'] ?? [];
        $labels = $groupingData['labels'] ?? [];
        $hidden = $groupingData['hidden'] ?? [];
        $values = $groupingData['values'] ?? [];
        $types = $groupingData['types'] ?? [];

        if (empty($names) || empty($labels)) {
            return null;
        }

        // Get or create shared header for this table type
        $header = $this->getOrCreateTableHeader($varName, $names, $labels, $hidden, $types);

        // Create table name from variable name
        $tableName = $this->formatTableName($varName);

        // Create table
        $table = new ExportTable(
            $varName,
            $tableName,
            $header,
            $model->get('id'),
            [
                'source_model' => get_class($model),
                'reference_column' => $this->referenceColumn
            ]
        );

        // Populate table with data
        $this->populateTableFromGroupingData($table, $values, $names, $hidden, $types, $groupingData, $options, $model);

        // Apply filtering: skip tables with only 1 row containing only empty/zero values
        if ($this->shouldSkipTable($table)) {
            return null;
        }

        return $table;
    }

    /**
     * Create a table from GT2 data
     *
     * @param \Model $model
     * @param string $varName
     * @param array $gt2Data
     * @param array $options
     * @return ExportTable|null
     */
    private function createTableFromGT2Data(\Model $model, string $varName, array $gt2Data, array $options): ?ExportTable
    {
        // Extract table structure from GT2 data
        $vars = $gt2Data['vars'] ?? [];
        $values = $gt2Data['values'] ?? [];

        if (empty($vars)) {
            return null;
        }

        // Get or create shared header for this table type
        $header = $this->getOrCreateGT2TableHeader($varName, $vars);

        // Create table name from variable name
        $tableName = $this->formatTableName($varName);

        // Create table
        $table = new ExportTable(
            $varName,
            $tableName,
            $header,
            $model->get('id'),
            [
                'source_model' => get_class($model),
                'table_type' => 'gt2',
                'reference_column' => $this->referenceColumn
            ]
        );

        // Populate table with data
        $this->populateTableFromGT2Data($table, $values, $vars, $options, $model);

        // Apply filtering: skip tables with only 1 row containing only empty/zero values
        if ($this->shouldSkipTable($table)) {
            return null;
        }

        return $table;
    }

    /**
     * Get or create a shared table header for a table type
     *
     * @param string $tableType
     * @param array $names
     * @param array $labels
     * @param array $hidden
     * @param array $types
     * @return ExportHeader
     */
    private function getOrCreateTableHeader(string $tableType, array $names, array $labels, array $hidden, array $types = []): ExportHeader
    {
        // Check if we already have a header for this table type
        if (isset($this->tableHeaders[$tableType])) {
            return $this->tableHeaders[$tableType];
        }

        // Create new header
        $header = new ExportHeader();

        // Add reference column first
        $referenceColumn = new ExportColumn(
            $this->referenceColumn['name'],
            $this->referenceColumn['label'],
            ExportValue::TYPE_STRING
        );
        $header->addColumn($referenceColumn);

        foreach ($names as $index => $varName) {
            // Skip hidden columns (use strict comparison to avoid PHP loose comparison bugs)
            if (in_array($varName, $hidden, true) || in_array($index, $hidden, true)) {
                continue;
            }

            $label = $labels[$index] ?? $varName;
            // Use actual type from grouping data if available, otherwise guess from variable name
            if (isset($types[$index])) {
                try {
                    $type = $this->convertFieldTypeToValueType($types[$index]);
                } catch (\InvalidArgumentException $e) {
                    // Skip unsupported field types
                    continue;
                }
            } else {
                $type = $this->guessColumnType($varName);
            }

            $column = new ExportColumn($varName, $label, $type);
            $header->addColumn($column);
        }

        // Cache the header for reuse
        $this->tableHeaders[$tableType] = $header;

        return $header;
    }

    /**
     * Get or create a shared table header for GT2 table type
     *
     * @param string $tableType
     * @param array $vars GT2 vars array with variable definitions
     * @return ExportHeader
     */
    private function getOrCreateGT2TableHeader(string $tableType, array $vars): ExportHeader
    {
        // Check if we already have a header for this table type
        if (isset($this->tableHeaders[$tableType])) {
            return $this->tableHeaders[$tableType];
        }

        // Create new header
        $header = new ExportHeader();

        // Add reference column first
        $referenceColumn = new ExportColumn(
            $this->referenceColumn['name'],
            $this->referenceColumn['label'],
            ExportValue::TYPE_STRING
        );
        $header->addColumn($referenceColumn);

        // Sort variables by position
        $sortedVars = $this->sortGT2VarsByPosition($vars);

        foreach ($sortedVars as $varName => $varData) {
            // Skip hidden columns
            if (isset($varData['hidden']) && $varData['hidden'] === '1') {
                continue;
            }

            $label = $varData['label'] ?? $varName;
            try {
                $type = $this->convertFieldTypeToValueType($varData['type'] ?? 'text');
            } catch (\InvalidArgumentException $e) {
                // Skip unsupported field types
                continue;
            }

            $column = new ExportColumn($varName, $label, $type);
            $header->addColumn($column);
        }

        // Cache the header for reuse
        $this->tableHeaders[$tableType] = $header;

        return $header;
    }

    /**
     * Sort GT2 variables by position
     *
     * @param array $vars
     * @return array Sorted variables array
     */
    private function sortGT2VarsByPosition(array $vars): array
    {
        // Create array with position as key for sorting
        $varsWithPosition = [];
        foreach ($vars as $varName => $varData) {
            $position = isset($varData['position']) ? (int)$varData['position'] : 999;
            $varsWithPosition[$position][$varName] = $varData;
        }

        // Sort by position
        ksort($varsWithPosition);

        // Flatten back to single array maintaining order
        $sortedVars = [];
        foreach ($varsWithPosition as $positionGroup) {
            foreach ($positionGroup as $varName => $varData) {
                $sortedVars[$varName] = $varData;
            }
        }

        return $sortedVars;
    }

    /**
     * Format table name from variable name
     *
     * @param string $varName
     * @return string
     */
    private function formatTableName(string $varName): string
    {
        // Convert snake_case or camelCase to Title Case
        $name = str_replace(['_', '-'], ' ', $varName);
        $name = ucwords($name);

        return $name;
    }

    /**
     * Convert field type to value type
     *
     * @param string $fieldType Field type from grouping data
     * @return string Value type for ExportColumn
     */
    private function convertFieldTypeToValueType(string $fieldType): string
    {
        switch ($fieldType) {
            case 'date':
                return ExportValue::TYPE_DATE;

            case 'datetime':
                return ExportValue::TYPE_DATETIME;

            case 'text':
            case 'textarea':
            case 'dropdown':
            case 'radio':
            case 'checkbox_group':
            case 'config':
            case 'button':
            case 'autocompleter':
            case 'time':
            //case 'file_upload':
            //case 'formula':
            //case 'map':
                return ExportValue::TYPE_STRING;
            default:
                throw new \InvalidArgumentException("Unsupported field type: $fieldType");
        }
    }

    /**
     * Guess column type from variable name
     *
     * @param string $varName
     * @return string
     */
    private function guessColumnType(string $varName): string
    {
        $varName = strtolower($varName);

        // Date/datetime patterns
        if (preg_match('/(date|time|created|updated|modified)/', $varName)) {
            if (preg_match('/(datetime|timestamp|created_at|updated_at)/', $varName)) {
                return ExportValue::TYPE_DATETIME;
            }
            return ExportValue::TYPE_DATE;
        }

        // Numeric patterns
        if (preg_match('/(id|count|quantity|amount|price|total|sum|number)/', $varName)) {
            if (preg_match('/(price|amount|total|sum|rate|cost)/', $varName)) {
                return ExportValue::TYPE_FLOAT;
            }
            return ExportValue::TYPE_INTEGER;
        }

        // Boolean patterns
        if (preg_match('/(is_|has_|can_|active|enabled|disabled|visible|hidden)/', $varName)) {
            return ExportValue::TYPE_BOOLEAN;
        }

        // Default to string
        return ExportValue::TYPE_STRING;
    }

    /**
     * Populate table from grouping data
     *
     * @param ExportTable $table
     * @param array $values Two-dimensional array with rows and values
     * @param array $names Column names
     * @param array $hidden Hidden column indices/names
     * @param array $types Column types
     * @param array $groupingData Full grouping data including options
     * @param array $options
     * @param \Model $model
     */
    private function populateTableFromGroupingData(ExportTable $table, array $values, array $names, array $hidden, array $types, array $groupingData, array $options, \Model $model): void
    {
        foreach ($values as $rowData) {
            $record = $this->createRecordFromRowData($rowData, $names, $hidden, $types, $groupingData, $options, $model);
            if ($record) {
                $table->addRecord($record, false);
            }
        }
    }

    /**
     * Populate table from GT2 data
     *
     * @param ExportTable $table
     * @param array $values Associative array with row IDs as keys and row data as values
     * @param array $vars GT2 vars array with variable definitions
     * @param array $options
     * @param \Model $model
     */
    private function populateTableFromGT2Data(ExportTable $table, array $values, array $vars, array $options, \Model $model): void
    {
        // Sort variables by position to maintain consistent column order
        $sortedVars = $this->sortGT2VarsByPosition($vars);

        foreach ($values as $rowId => $rowData) {
            $record = $this->createRecordFromGT2RowData($rowData, $sortedVars, $options, $model);
            if ($record) {
                $table->addRecord($record, false);
            }
        }
    }

    /**
     * Create export record from GT2 row data
     *
     * @param array $rowData Associative array with variable names as keys
     * @param array $sortedVars GT2 vars array sorted by position
     * @param array $options
     * @param \Model $model
     * @return ExportRecord|null
     */
    private function createRecordFromGT2RowData(array $rowData, array $sortedVars, array $options, \Model $model): ?ExportRecord
    {
        $record = new ExportRecord();

        // Add reference column value first
        $referenceValue = $model->get($this->referenceColumn['name']) ?? '';
        $record->addValue($this->referenceColumn['name'], $referenceValue, ExportValue::TYPE_STRING);

        foreach ($sortedVars as $varName => $varData) {
            // Skip hidden columns
            if (isset($varData['hidden']) && $varData['hidden'] === '1') {
                continue;
            }

            // Get value by variable name, handle missing values
            $value = $rowData[$varName] ?? null;
            $fieldType = $varData['type'] ?? 'text';
            try {
                $type = $this->convertFieldTypeToValueType($fieldType);
            } catch (\InvalidArgumentException $e) {
                // Skip unsupported field types
                continue;
            }

            // Resolve option label for dropdown, radio, and checkbox_group fields
            $resolvedValue = $this->resolveOptionLabelFromVarData($value, $fieldType, $varData);
            $formattedValue = $this->formatValue($resolvedValue, $type, null, $options);

            $record->addValue($varName, $formattedValue, $type);
        }

        return $record;
    }

    /**
     * Create export record from row data
     *
     * @param array $rowData
     * @param array $names
     * @param array $hidden
     * @param array $types
     * @param array $groupingData Full grouping data including options
     * @param array $options
     * @param \Model $model
     * @return ExportRecord|null
     */
    private function createRecordFromRowData(array $rowData, array $names, array $hidden, array $types, array $groupingData, array $options, \Model $model): ?ExportRecord
    {
        $record = new ExportRecord();

        // Add reference column value first
        $referenceValue = $model->get($this->referenceColumn['name']) ?? '';
        $record->addValue($this->referenceColumn['name'], $referenceValue, ExportValue::TYPE_STRING);

        foreach ($names as $index => $varName) {
            // Skip hidden columns (use strict comparison to avoid PHP loose comparison bugs)
            if (in_array($varName, $hidden, true) || in_array($index, $hidden, true)) {
                continue;
            }

            // Get value by index, handle missing values
            $value = $rowData[$index] ?? null;
            // Use actual type from grouping data if available, otherwise guess from variable name
            $fieldType = null;
            if (isset($types[$index])) {
                $fieldType = $types[$index];
                try {
                    $type = $this->convertFieldTypeToValueType($fieldType);
                } catch (\InvalidArgumentException $e) {
                    // Skip unsupported field types
                    continue;
                }
            } else {
                $type = $this->guessColumnType($varName);
            }

            // Resolve option label for dropdown, radio, and checkbox_group fields
            $resolvedValue = $this->resolveOptionLabel($value, $fieldType, $varName, $groupingData);
            $formattedValue = $this->formatValue($resolvedValue, $type, null, $options);

            $record->addValue($varName, $formattedValue, $type);
        }

        return $record;
    }



    /**
     * Resolve option label for dropdown, radio, and checkbox_group fields from grouping data
     *
     * @param mixed $value The raw value from the data
     * @param string|null $fieldType The field type (dropdown, radio, checkbox_group, etc.)
     * @param string $varName The variable name
     * @param array $groupingData The full grouping data with options at $groupingData[$varName]['options']
     * @return mixed The resolved label or original value if not an option field
     */
    private function resolveOptionLabel($value, ?string $fieldType, string $varName, array $groupingData)
    {
        // Only process option-based field types
        if (!in_array($fieldType, ['dropdown', 'radio', 'checkbox_group'], true)) {
            return $value;
        }

        // Check if options data exists for this variable
        if (!isset($groupingData[$varName]['options']) || !is_array($groupingData[$varName]['options'])) {
            return $value;
        }

        $options = $groupingData[$varName]['options'];

        // Search for matching option value and return its label
        foreach ($options as $option) {
            if (isset($option['option_value']) && isset($option['label']) &&
                (string)$option['option_value'] === (string)$value) {
                return $option['label'];
            }
        }

        // If no matching option found, return original value
        return $value;
    }

    /**
     * Resolve option label for dropdown, radio, and checkbox_group fields from GT2 var data
     *
     * @param mixed $value The raw value from the data
     * @param string $fieldType The field type (dropdown, radio, checkbox_group, etc.)
     * @param array $varData The variable data including options
     * @return mixed The resolved label or original value if not an option field
     */
    private function resolveOptionLabelFromVarData($value, string $fieldType, array $varData)
    {
        // Only process option-based field types
        if (!in_array($fieldType, ['dropdown', 'radio', 'checkbox_group'], true)) {
            return $value;
        }

        // Check if options data exists for this variable
        if (!isset($varData['options']) || !is_array($varData['options'])) {
            return $value;
        }

        $options = $varData['options'];

        // Search for matching option value and return its label
        foreach ($options as $option) {
            if (isset($option['option_value']) && isset($option['label']) &&
                (string)$option['option_value'] === (string)$value) {
                return $option['label'];
            }
        }

        // If no matching option found, return original value
        return $value;
    }

    /**
     * Format value based on type
     *
     * @param mixed $value
     * @param string $type
     * @param string|null $format
     * @param array $options
     * @return mixed
     */
    private function formatValue($value, string $type, ?string $format, array $options)
    {
        if ($value === null) {
            return null;
        }

        switch ($type) {
            case ExportValue::TYPE_DATE:
            case 'date':
                if ($value instanceof \DateTimeInterface) {
                    return $value;
                } elseif (is_string($value) && strtotime($value) !== false) {
                    return new \DateTime($value);
                }
                break;

            case ExportValue::TYPE_DATETIME:
            case 'datetime':
                if ($value instanceof \DateTimeInterface) {
                    return $value;
                } elseif (is_string($value) && strtotime($value) !== false) {
                    return new \DateTime($value);
                }
                break;

            case ExportValue::TYPE_INTEGER:
                return (int) $value;

            case ExportValue::TYPE_FLOAT:
                return (float) $value;

            case ExportValue::TYPE_BOOLEAN:
                return (bool) $value;
        }

        return $value;
    }

    /**
     * {@inheritdoc}
     */
    public function getTableConfiguration(string $tableType): array
    {
        // Return basic configuration since we auto-discover structure
        return [
            'name' => $this->formatTableName($tableType),
            'auto_discovered' => true
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function validateRecord($record, array $requestedTableTypes = []): bool
    {
        if (!($record instanceof \Model)) {
            return false;
        }

        // Since we auto-discover tables, we can always try to extract them
        // The validation is done during extraction
        return true;
    }

    /**
     * Check if a table should be skipped based on filtering criteria
     *
     * @param ExportTable $table
     * @return bool True if the table should be skipped
     */
    private function shouldSkipTable(ExportTable $table): bool
    {
        // Only apply filtering to tables with exactly 1 record
        if ($table->count() !== 1) {
            return false;
        }

        $records = $table->getRecords();
        $record = reset($records);

        return $this->isTableRowEmpty($record);
    }

    /**
     * Check if a table record contains only empty and zero values
     * Excludes the reference column from the check since it's always populated
     *
     * @param ExportRecord $record
     * @return bool True if the record contains only empty/zero values (excluding reference column)
     */
    private function isTableRowEmpty(ExportRecord $record): bool
    {
        $rawValues = $record->getRawValues();
        $referenceColumnName = $this->referenceColumn['name'];

        // Skip the reference column (always first) when checking for empty values
        foreach ($rawValues as $index => $value) {
            // Skip the reference column by checking if this is the first value
            // and it corresponds to the reference column
            if ($index === 0 && $record->hasValue($referenceColumnName)) {
                continue;
            }

            if (!$this->isValueEmptyOrZero($value)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Check if a value is considered empty or zero
     *
     * @param mixed $value
     * @return bool True if the value is empty or zero
     */
    private function isValueEmptyOrZero($value): bool
    {
        // Null values are considered empty
        if ($value === null) {
            return true;
        }

        // Empty strings are considered empty
        if ($value === '') {
            return true;
        }

        // Zero values (integer and float) are considered empty
        if ($value === 0 || $value === 0.0) {
            return true;
        }

        // Boolean false is considered empty
        if ($value === false) {
            return true;
        }

        // String values that should be considered empty
        if (is_string($value)) {
            $emptyDateTimeValues = [
                '0:00',
                '00.00.0000',
                '00.00.0000 00:00',
                '00.00.0000 00:00:00',
                '01-01-1970',
                '01-01-1970 00:00:00'
            ];

            if (in_array($value, $emptyDateTimeValues, true)) {
                return true;
            }
        }

        // All other values are not considered empty
        return false;
    }
}
