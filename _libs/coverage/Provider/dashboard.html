<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /var/www/Nzoom-Hella/_libs/Nzoom/Export/Provider</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../_css/bootstrap.min.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="../_css/nv.d3.min.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="../_css/style.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../index.html">/var/www/Nzoom-Hella/_libs/Nzoom/Export</a></li>
         <li class="breadcrumb-item"><a href="index.html">Provider</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ModelTableProvider.php.html#63"><abbr title="Nzoom\Export\Provider\ModelTableProvider::getReferenceColumn">getReferenceColumn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ModelTableProvider.php.html#642"><abbr title="Nzoom\Export\Provider\ModelTableProvider::resolveOptionLabel">resolveOptionLabel</abbr></a></td><td class="text-right">20%</td></tr>
       <tr><td><a href="ModelTableProvider.php.html#676"><abbr title="Nzoom\Export\Provider\ModelTableProvider::resolveOptionLabelFromVarData">resolveOptionLabelFromVarData</abbr></a></td><td class="text-right">20%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ModelTableProvider.php.html#642"><abbr title="Nzoom\Export\Provider\ModelTableProvider::resolveOptionLabel">resolveOptionLabel</abbr></a></td><td class="text-right">40</td></tr>
       <tr><td><a href="ModelTableProvider.php.html#676"><abbr title="Nzoom\Export\Provider\ModelTableProvider::resolveOptionLabelFromVarData">resolveOptionLabelFromVarData</abbr></a></td><td class="text-right">40</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 9.2.32</a> using <a href="https://secure.php.net/" target="_top">PHP 7.4.33</a> and <a href="https://phpunit.de/">PHPUnit 9.6.23</a> at Tue Jun 24 16:46:51 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../_js/jquery.min.js?v=9.2.32" type="text/javascript"></script>
  <script src="../_js/d3.min.js?v=9.2.32" type="text/javascript"></script>
  <script src="../_js/nv.d3.min.js?v=9.2.32" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([0,0,0,0,0,0,0,0,0,0,1,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([1,0,0,2,0,0,0,0,1,0,2,19], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[91.83673469387756,134,"<a href=\"ModelTableProvider.php.html#18\">Nzoom\\Export\\Provider\\ModelTableProvider<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[100,1,"<a href=\"ModelTableProvider.php.html#48\">Nzoom\\Export\\Provider\\ModelTableProvider::__construct<\/a>"],[0,1,"<a href=\"ModelTableProvider.php.html#63\">Nzoom\\Export\\Provider\\ModelTableProvider::getReferenceColumn<\/a>"],[100,1,"<a href=\"ModelTableProvider.php.html#73\">Nzoom\\Export\\Provider\\ModelTableProvider::getDefaultOptions<\/a>"],[70.58823529411765,10,"<a href=\"ModelTableProvider.php.html#85\">Nzoom\\Export\\Provider\\ModelTableProvider::getTablesForRecord<\/a>"],[100,10,"<a href=\"ModelTableProvider.php.html#128\">Nzoom\\Export\\Provider\\ModelTableProvider::discoverGroupingVariables<\/a>"],[100,4,"<a href=\"ModelTableProvider.php.html#180\">Nzoom\\Export\\Provider\\ModelTableProvider::createTableFromGroupingData<\/a>"],[95.23809523809523,3,"<a href=\"ModelTableProvider.php.html#231\">Nzoom\\Export\\Provider\\ModelTableProvider::createTableFromGT2Data<\/a>"],[100,7,"<a href=\"ModelTableProvider.php.html#281\">Nzoom\\Export\\Provider\\ModelTableProvider::getOrCreateTableHeader<\/a>"],[100,6,"<a href=\"ModelTableProvider.php.html#335\">Nzoom\\Export\\Provider\\ModelTableProvider::getOrCreateGT2TableHeader<\/a>"],[100,5,"<a href=\"ModelTableProvider.php.html#386\">Nzoom\\Export\\Provider\\ModelTableProvider::sortGT2VarsByPosition<\/a>"],[100,1,"<a href=\"ModelTableProvider.php.html#415\">Nzoom\\Export\\Provider\\ModelTableProvider::formatTableName<\/a>"],[100,13,"<a href=\"ModelTableProvider.php.html#430\">Nzoom\\Export\\Provider\\ModelTableProvider::convertFieldTypeToValueType<\/a>"],[100,6,"<a href=\"ModelTableProvider.php.html#463\">Nzoom\\Export\\Provider\\ModelTableProvider::guessColumnType<\/a>"],[100,3,"<a href=\"ModelTableProvider.php.html#504\">Nzoom\\Export\\Provider\\ModelTableProvider::populateTableFromGroupingData<\/a>"],[100,3,"<a href=\"ModelTableProvider.php.html#523\">Nzoom\\Export\\Provider\\ModelTableProvider::populateTableFromGT2Data<\/a>"],[100,5,"<a href=\"ModelTableProvider.php.html#545\">Nzoom\\Export\\Provider\\ModelTableProvider::createRecordFromGT2RowData<\/a>"],[100,6,"<a href=\"ModelTableProvider.php.html#591\">Nzoom\\Export\\Provider\\ModelTableProvider::createRecordFromRowData<\/a>"],[20,8,"<a href=\"ModelTableProvider.php.html#642\">Nzoom\\Export\\Provider\\ModelTableProvider::resolveOptionLabel<\/a>"],[20,8,"<a href=\"ModelTableProvider.php.html#676\">Nzoom\\Export\\Provider\\ModelTableProvider::resolveOptionLabelFromVarData<\/a>"],[100,15,"<a href=\"ModelTableProvider.php.html#711\">Nzoom\\Export\\Provider\\ModelTableProvider::formatValue<\/a>"],[100,1,"<a href=\"ModelTableProvider.php.html#752\">Nzoom\\Export\\Provider\\ModelTableProvider::getTableConfiguration<\/a>"],[100,2,"<a href=\"ModelTableProvider.php.html#764\">Nzoom\\Export\\Provider\\ModelTableProvider::validateRecord<\/a>"],[100,2,"<a href=\"ModelTableProvider.php.html#781\">Nzoom\\Export\\Provider\\ModelTableProvider::shouldSkipTable<\/a>"],[100,5,"<a href=\"ModelTableProvider.php.html#801\">Nzoom\\Export\\Provider\\ModelTableProvider::isTableRowEmpty<\/a>"],[95,8,"<a href=\"ModelTableProvider.php.html#828\">Nzoom\\Export\\Provider\\ModelTableProvider::isValueEmptyOrZero<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>
